# 💻 Desenvolvimento Local (sem Docker da aplicação)

Este guia mostra como executar a aplicação localmente sem Docker, mantendo apenas MongoDB e Redis em containers.

## 🎯 Vantagens da Execução Local

- ✅ **Desenvolvimento mais rápido** - sem rebuild de containers
- ✅ **Debug mais fácil** - acesso direto ao Node.js
- ✅ **Hot reload nativo** - mudanças instantâneas
- ✅ **Menos recursos** - apenas app local + deps Docker
- ✅ **IDE integration** - melhor suporte para debugging

## 📋 Pré-requisitos

### ✅ Já Instalado
- ✅ Node.js 20.18.0
- ✅ npm 10.8.2
- ✅ Docker & Docker Compose

### 🔧 Verificar Instalação
```bash
node --version  # v20.18.0
npm --version   # 10.8.2
docker --version
```

## 🚀 Execução Rápida

### 1. **Modo Desenvolvimento (Recomendado)**
```bash
# Inicia MongoDB + Redis + App local com hot reload
make local-dev
```

### 2. **Modo Produção Local**
```bash
# Inicia MongoDB + Redis + App compilado
make local-start
```

## 📝 Comandos Detalhados

### 🔧 **Dependências**
```bash
# Instalar dependências npm
make local-install

# Iniciar apenas MongoDB + Redis
make local-deps
```

### 🏗️ **Build & Execução**
```bash
# Compilar aplicação
make local-build

# Executar app compilado
make local-start
```

### 🛑 **Parar Serviços**
```bash
# Parar MongoDB + Redis
make local-stop

# Limpar tudo (para + remove containers)
make local-clean
```

## 🔍 Execução Manual (Passo a Passo)

### 1. **Preparar Ambiente**
```bash
# Copiar configuração de desenvolvimento
cp .env.development .env

# Instalar dependências
npm install
```

### 2. **Iniciar Dependências**
```bash
# Iniciar MongoDB + Redis com Docker
docker-compose up -d mongo redis

# Verificar se estão rodando
docker ps
```

### 3. **Executar Aplicação**
```bash
# Modo desenvolvimento (hot reload)
npm run start:dev

# OU modo produção
npm run build
npm run start:prod
```

## 🌐 Acesso aos Serviços

- **API**: http://localhost:3000/api/v1
- **Documentação**: http://localhost:3000/docs
- **Health Check**: http://localhost:3000/api/v1/health
- **MongoDB**: mongodb://localhost:27017
- **Redis**: localhost:6379

## 🔧 Configuração Local

### Arquivo `.env` (copiado de `.env.development`)
```env
NODE_ENV=development
PORT=3000
API_PREFIX=api/v1

# Dependências locais
MONGODB_URI=mongodb://localhost:27017/cashback-comparison-dev
REDIS_HOST=localhost
REDIS_PORT=6379

# Configurações de desenvolvimento
LOG_LEVEL=debug
CACHE_COMPARISON_TTL=300
THROTTLE_LIMIT=1000
```

## 🐛 Debug & Desenvolvimento

### 1. **Debug com VS Code**
```json
// .vscode/launch.json
{
  "type": "node",
  "request": "launch",
  "name": "Debug NestJS",
  "program": "${workspaceFolder}/dist/main.js",
  "env": {
    "NODE_ENV": "development"
  },
  "console": "integratedTerminal",
  "restart": true,
  "runtimeArgs": ["--nolazy"],
  "sourceMaps": true
}
```

### 2. **Hot Reload**
```bash
# O comando local-dev já inclui hot reload
make local-dev

# Ou manualmente
npm run start:dev
```

### 3. **Logs em Tempo Real**
```bash
# Logs da aplicação
tail -f logs/app-dev.log

# Ou usar o comando do Makefile
make logs
```

## 🧪 Testes Locais

```bash
# Testes unitários
npm run test

# Testes e2e
npm run test:e2e

# Testes com coverage
npm run test:cov

# Testes da API
make test-api
```

## ⚡ Comandos Úteis

```bash
# Ver ajuda completa
make help

# Status do sistema
make monitor

# Verificar saúde
make health

# Limpar cache npm
npm cache clean --force

# Reinstalar dependências
rm -rf node_modules package-lock.json
npm install
```

## 🔄 Workflow Recomendado

1. **Primeira vez**:
   ```bash
   make local-install  # Instalar deps
   make local-dev      # Iniciar tudo
   ```

2. **Desenvolvimento diário**:
   ```bash
   make local-dev      # Já inicia deps + app
   ```

3. **Ao finalizar**:
   ```bash
   make local-stop     # Para deps (app para com Ctrl+C)
   ```

## 🆚 Docker vs Local

| Aspecto | Docker | Local |
|---------|--------|-------|
| **Setup** | `make dev` | `make local-dev` |
| **Velocidade** | Mais lento | Mais rápido |
| **Debug** | Limitado | Completo |
| **Recursos** | Mais pesado | Mais leve |
| **Isolamento** | Total | Parcial |
| **Produção** | Idêntico | Diferente |

## 🎯 Quando Usar Cada Modo

### 🐳 **Use Docker quando**:
- Primeira execução
- Testes de integração
- Simulação de produção
- Trabalho em equipe

### 💻 **Use Local quando**:
- Desenvolvimento ativo
- Debug intensivo
- Performance crítica
- Desenvolvimento de features

---

**💡 Dica**: Comece com `make local-dev` para desenvolvimento rápido!
