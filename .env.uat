# 🧪 UAT ENVIRONMENT
# Para testes de aceitação e homologação

# Application Configuration
NODE_ENV=staging
PORT=4000
API_PREFIX=api/v1

# Database Configuration (Cloud/Staging)
MONGODB_URI=mongodb://mongo-uat:27017/cashback-comparison-uat
MONGODB_TEST_URI=mongodb://mongo-uat:27017/cashback-comparison-uat-test

# Redis Configuration (Cloud/Staging)
REDIS_HOST=redis-uat
REDIS_PORT=6379
REDIS_PASSWORD=${REDIS_UAT_PASSWORD}
REDIS_DB=0
REDIS_KEY_PREFIX=cashback:uat:

# Cache Configuration (Próximo à produção)
CACHE_COMPARISON_TTL=600
CACHE_OFFERS_TTL=2400
CACHE_PLATFORMS_TTL=7200
CACHE_ANALYTICS_TTL=1800
CACHE_HEALTH_TTL=300
CACHE_MAX_COMPARISONS=5000
CACHE_MAX_OFFERS=25000
CACHE_MAX_PLATFORMS=100
CACHE_MAX_ANALYTICS=500

# Rate Limiting (Restritivo como produção)
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging
LOG_LEVEL=info
LOG_FILE_PATH=logs/app-uat.log

# External APIs (Staging)
# SAMPLE_PLATFORM_API_KEY=${UAT_PLATFORM_API_KEY}

# UAT Features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=false
CORS_ORIGIN=https://uat.cashboost.com.br,https://admin-uat.cashboost.com.br

# Monitoring
ENABLE_METRICS=true
METRICS_PORT=9090

# New Relic Configuration
NEW_RELIC_ENABLED=true
NEW_RELIC_APP_NAME=Cashback-Platform-UAT
NEW_RELIC_LICENSE_KEY=${NEW_RELIC_UAT_LICENSE_KEY}
NEW_RELIC_LOG_LEVEL=info
