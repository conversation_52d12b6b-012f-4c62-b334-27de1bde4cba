# 🚀 PRODUCTION ENVIRONMENT
# Para ambiente de produção com alta disponibilidade

services:
  # Aplicação NestJS (múltiplas instâncias)
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    restart: unless-stopped
    environment:
      - NODE_ENV=production
    env_file:
      - .env.prod
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mongo
      - redis
    networks:
      - cashback-prod
    deploy:
      replicas: 3
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # Load Balancer
  nginx:
    image: nginx:alpine
    container_name: cashback-nginx-prod
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/prod.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - app
    networks:
      - cashback-prod

  # MongoDB (Replica Set recomendado para produção)
  mongo:
    image: mongo:7.0
    container_name: cashback-mongo-prod
    restart: unless-stopped
    environment:
      - MONGO_INITDB_DATABASE=cashback-comparison
    volumes:
      - mongo_prod_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - cashback-prod
    deploy:
      resources:
        limits:
          cpus: '2.0'
          memory: 2G
        reservations:
          cpus: '1.0'
          memory: 1G

  # Redis (Cluster recomendado para produção)
  redis:
    image: redis:7.2-alpine
    container_name: cashback-redis-prod
    restart: unless-stopped
    volumes:
      - redis_prod_data:/data
      - ./redis/prod.conf:/usr/local/etc/redis/redis.conf:ro
    networks:
      - cashback-prod
    command: redis-server /usr/local/etc/redis/redis.conf
    deploy:
      resources:
        limits:
          cpus: '1.0'
          memory: 1G
        reservations:
          cpus: '0.5'
          memory: 512M

  # Monitoring - Prometheus
  prometheus:
    image: prom/prometheus:latest
    container_name: cashback-prometheus-prod
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_prod_data:/prometheus
    networks:
      - cashback-prod
    command:
      - '--config.file=/etc/prometheus/prometheus.yml'
      - '--storage.tsdb.path=/prometheus'
      - '--web.console.libraries=/etc/prometheus/console_libraries'
      - '--web.console.templates=/etc/prometheus/consoles'
      - '--storage.tsdb.retention.time=200h'
      - '--web.enable-lifecycle'

  # Monitoring - Grafana
  grafana:
    image: grafana/grafana:latest
    container_name: cashback-grafana-prod
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - GF_SECURITY_ADMIN_PASSWORD=${GRAFANA_ADMIN_PASSWORD}
    volumes:
      - grafana_prod_data:/var/lib/grafana
      - ./monitoring/grafana/dashboards:/etc/grafana/provisioning/dashboards:ro
      - ./monitoring/grafana/datasources:/etc/grafana/provisioning/datasources:ro
    networks:
      - cashback-prod
    depends_on:
      - prometheus

  # Log Aggregation - Loki
  loki:
    image: grafana/loki:latest
    container_name: cashback-loki-prod
    restart: unless-stopped
    ports:
      - "3100:3100"
    volumes:
      - loki_prod_data:/loki
      - ./monitoring/loki.yml:/etc/loki/local-config.yaml:ro
    networks:
      - cashback-prod
    command: -config.file=/etc/loki/local-config.yaml

volumes:
  mongo_prod_data:
    driver: local
  redis_prod_data:
    driver: local
  prometheus_prod_data:
    driver: local
  grafana_prod_data:
    driver: local
  loki_prod_data:
    driver: local

networks:
  cashback-prod:
    driver: bridge
