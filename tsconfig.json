{"compilerOptions": {"module": "commonjs", "declaration": true, "removeComments": true, "emitDecoratorMetadata": true, "experimentalDecorators": true, "allowSyntheticDefaultImports": true, "esModuleInterop": true, "target": "ES2023", "lib": ["ES2023"], "sourceMap": true, "outDir": "./dist", "baseUrl": "./", "incremental": true, "skipLibCheck": true, "strict": false, "strictNullChecks": true, "strictFunctionTypes": false, "strictBindCallApply": false, "strictPropertyInitialization": false, "noImplicitAny": false, "noImplicitReturns": false, "noImplicitThis": false, "noUnusedLocals": false, "noUnusedParameters": false, "exactOptionalPropertyTypes": false, "noImplicitOverride": false, "noPropertyAccessFromIndexSignature": false, "noUncheckedIndexedAccess": false, "noFallthroughCasesInSwitch": false, "forceConsistentCasingInFileNames": true, "resolveJsonModule": true, "isolatedModules": true, "useDefineForClassFields": false}, "include": ["src/**/*", "test/**/*"], "exclude": ["node_modules", "dist"]}