# 🚀 PRODUCTION ENVIRONMENT
# Para ambiente de produção

# Application Configuration
NODE_ENV=production
PORT=3000
API_PREFIX=api/v1

# Database Configuration (Cloud Production)
MONGODB_URI=${MONGODB_PROD_URI}
MONGODB_TEST_URI=${MONGODB_PROD_TEST_URI}

# Redis Configuration (Cloud Production)
REDIS_HOST=${REDIS_PROD_HOST}
REDIS_PORT=${REDIS_PROD_PORT}
REDIS_PASSWORD=${REDIS_PROD_PASSWORD}
REDIS_DB=0
REDIS_KEY_PREFIX=cashback:prod:

# Cache Configuration (Otimizado para produção)
CACHE_COMPARISON_TTL=900
CACHE_OFFERS_TTL=3600
CACHE_PLATFORMS_TTL=7200
CACHE_ANALYTICS_TTL=1800
CACHE_HEALTH_TTL=300
CACHE_MAX_COMPARISONS=10000
CACHE_MAX_OFFERS=50000
CACHE_MAX_PLATFORMS=100
CACHE_MAX_ANALYTICS=1000

# Rate Limiting (Restritivo)
THROTTLE_TTL=60
THROTTLE_LIMIT=100

# Logging
LOG_LEVEL=warn
LOG_FILE_PATH=logs/app-prod.log

# External APIs (Production)
# SAMPLE_PLATFORM_API_KEY=${PROD_PLATFORM_API_KEY}

# Production Security
ENABLE_SWAGGER=false
ENABLE_DEBUG_ROUTES=false
CORS_ORIGIN=https://cashboost.com.br,https://admin.cashboost.com.br

# Monitoring & Observability
ENABLE_METRICS=true
METRICS_PORT=9090
ENABLE_TRACING=true
JAEGER_ENDPOINT=${JAEGER_PROD_ENDPOINT}

# Security
JWT_SECRET=${JWT_PROD_SECRET}
ENCRYPTION_KEY=${ENCRYPTION_PROD_KEY}
