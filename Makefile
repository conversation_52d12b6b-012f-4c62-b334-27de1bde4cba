# Makefile for Cashback Comparison Platform

.PHONY: help install build start start-dev stop clean test lint format docker-build docker-up docker-down logs local-dev local-deps local-install local-build local-start local-stop local-clean env-local env-dev env-uat env-prod deploy-dev deploy-uat deploy-prod

# Default target
help:
	@echo "🚀 Cashback Comparison Platform - Development Commands"
	@echo "======================================================"
	@echo ""
	@echo "📦 DESENVOLVIMENTO (RECOMENDADO - TUDO COM DOCKER):"
	@echo "  dev              - Inicia TODA a aplicação com Docker"
	@echo "  stop             - Para todos os serviços"
	@echo "  logs             - Ver logs da aplicação"
	@echo "  restart          - Reinicia a aplicação"
	@echo ""
	@echo "💻 DESENVOLVIMENTO LOCAL (sem Docker da app):"
	@echo "  local-dev        - App local + MongoDB/Redis Docker"
	@echo "  local-deps       - Inicia apenas MongoDB + Redis"
	@echo "  local-install    - Instala dependências npm"
	@echo "  local-build      - Compila aplicação"
	@echo "  local-start      - Inicia app compilado"
	@echo "  local-stop       - Para dependências"
	@echo "  local-clean      - Remove dependências"
	@echo ""
	@echo "🌍 AMBIENTES:"
	@echo "  env-local        - Configura ambiente LOCAL"
	@echo "  env-dev          - Configura ambiente DEV"
	@echo "  env-uat          - Configura ambiente UAT"
	@echo "  env-prod         - Configura ambiente PROD"
	@echo "  deploy-dev       - Deploy para DEV"
	@echo "  deploy-uat       - Deploy para UAT"
	@echo "  deploy-prod      - Deploy para PROD"
	@echo ""
	@echo "🧪 TESTES:"
	@echo "  test             - Testes unitários"
	@echo "  test-api         - Testes de integração da API"
	@echo ""
	@echo "🔧 UTILITÁRIOS:"
	@echo "  build            - Build da aplicação"
	@echo "  clean            - Limpar containers e volumes"
	@echo "  health           - Verificar saúde da aplicação"
	@echo "  monitor          - Status completo do sistema"
	@echo ""
	@echo "🌐 ACESSO:"
	@echo "  API: http://localhost:3000/api/v1"
	@echo "  Docs: http://localhost:3000/docs"
	@echo "  MongoDB UI: http://localhost:8081"
	@echo "  Redis UI: http://localhost:8082"

# 📦 COMANDOS PRINCIPAIS DE DESENVOLVIMENTO
dev:
	@echo "🚀 Iniciando aplicação completa com Docker..."
	docker-compose -f docker-compose.yml up -d
	@echo "✅ Aplicação iniciada!"
	@echo "🌐 Acesse: http://localhost:3000/api/v1"
	@echo "📚 Docs: http://localhost:3000/docs"

stop:
	@echo "🛑 Parando todos os serviços..."
	docker-compose -f docker-compose.dev.yml down
	@echo "✅ Serviços parados!"

restart:
	@echo "🔄 Reiniciando aplicação..."
	docker-compose -f docker-compose.dev.yml restart app
	@echo "✅ Aplicação reiniciada!"

logs:
	@echo "📋 Logs da aplicação:"
	docker-compose -f docker-compose.dev.yml logs -f app

# 🔧 COMANDOS DE BUILD E LIMPEZA
build:
	npm run build

clean:
	@echo "🧹 Limpando containers e volumes..."
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f
	@echo "✅ Limpeza concluída!"

test:
	npm run test

test-e2e:
	npm run test:e2e

test-cov:
	npm run test:cov

test-api:
	@echo "Running API integration tests..."
	./test-api.sh

lint:
	npm run lint

format:
	npm run format

# 🔍 COMANDOS DE MONITORAMENTO
health:
	@echo "🏥 Verificando saúde da aplicação..."
	@curl -f http://localhost:3000/api/v1/health || echo "❌ Aplicação não está saudável"

monitor:
	@echo "📊 Status do Sistema:"
	@echo "===================="
	@echo "🏥 Saúde da Aplicação:"
	@curl -s http://localhost:3000/api/v1/health || echo "❌ Aplicação offline"
	@echo ""
	@echo "💾 Status do Banco:"
	@curl -s http://localhost:3000/api/v1/health/database || echo "❌ Banco offline"
	@echo ""
	@echo "⚡ Status do Cache:"
	@curl -s http://localhost:3000/api/v1/health/cache || echo "❌ Cache offline"
	@echo ""
	@echo "🐳 Containers Docker:"
	@docker-compose -f docker-compose.dev.yml ps

# Database commands
db-seed:
	npm run db:seed

db-migrate:
	npm run db:migrate

db-reset:
	npm run db:reset

# Utility commands
logs-file:
	tail -f logs/app.log

logs-error:
	tail -f logs/error.log

# Legacy production deployment (removido - usar deploy-prod abaixo)

# 🚀 COMANDOS DE SETUP (APENAS SE NECESSÁRIO)
setup:
	@echo "⚙️ Configurando ambiente de desenvolvimento..."
	@cp .env.development .env 2>/dev/null || echo "Arquivo .env já existe"
	@echo "✅ Setup concluído!"
	@echo "💡 Execute 'make dev' para iniciar a aplicação"

# 💻 EXECUÇÃO LOCAL (sem Docker)
local-deps:
	@echo "🔧 Iniciando dependências locais (MongoDB + Redis)..."
	docker-compose up -d mongo redis
	@echo "✅ MongoDB e Redis iniciados!"
	@echo "🔗 MongoDB: mongodb://localhost:27017"
	@echo "🔗 Redis: localhost:6379"

local-install:
	@echo "📦 Instalando dependências..."
	npm install
	@echo "✅ Dependências instaladas!"

local-dev: local-deps
	@echo "🚀 Iniciando aplicação em modo desenvolvimento local..."
	@cp .env.development .env 2>/dev/null || true
	npm run start:dev

local-build:
	@echo "🔨 Compilando aplicação..."
	npm run build
	@echo "✅ Build concluído!"

local-start: local-deps local-build
	@echo "🚀 Iniciando aplicação em modo produção local..."
	@cp .env.development .env 2>/dev/null || true
	npm run start:prod

local-stop:
	@echo "🛑 Parando dependências locais..."
	docker-compose stop mongo redis
	@echo "✅ Dependências paradas!"

local-clean: local-stop
	@echo "🧹 Limpando dependências locais..."
	docker-compose down mongo redis
	@echo "✅ Limpeza concluída!"

# 🌍 GESTÃO DE AMBIENTES
env-local:
	@echo "💻 Configurando ambiente LOCAL..."
	@cp .env.local .env
	@echo "✅ Ambiente LOCAL configurado!"
	@echo "🔗 Use: make local-dev"

env-dev:
	@echo "🔧 Configurando ambiente DEV..."
	@cp .env.dev .env
	@echo "✅ Ambiente DEV configurado!"
	@echo "🔗 Use: make dev"

env-uat:
	@echo "🧪 Configurando ambiente UAT..."
	@cp .env.uat .env
	@echo "✅ Ambiente UAT configurado!"
	@echo "🔗 Use: make deploy-uat"

env-prod:
	@echo "🚀 Configurando ambiente PROD..."
	@cp .env.prod .env
	@echo "✅ Ambiente PROD configurado!"
	@echo "🔗 Use: make deploy-prod"

# 🚀 DEPLOY POR AMBIENTE
deploy-dev:
	@echo "🔧 Fazendo deploy para DEV..."
	docker-compose -f docker-compose.dev.yml up -d --build
	@echo "✅ Deploy DEV concluído!"
	@echo "🌐 Acesse: http://localhost:3000/api/v1"

deploy-uat:
	@echo "🧪 Fazendo deploy para UAT..."
	docker-compose -f docker-compose.uat.yml up -d --build
	@echo "✅ Deploy UAT concluído!"
	@echo "🌐 Acesse: http://uat.cashboost.com.br/api/v1"

deploy-prod:
	@echo "🚀 Fazendo deploy para PRODUÇÃO..."
	@echo "⚠️  ATENÇÃO: Deploy para produção!"
	@read -p "Confirma o deploy? (y/N): " confirm && [ "$$confirm" = "y" ]
	docker-compose -f docker-compose.prod.yml up -d --build
	@echo "✅ Deploy PROD concluído!"
	@echo "🌐 Acesse: https://cashboost.com.br/api/v1"

# CI/CD helpers
ci-test:
	npm ci
	npm run test
	npm run test:e2e

ci-build:
	npm run build
	docker build -t cashback-comparison-platform .

# Monitoring (detailed)
monitor-detailed:
	@echo "Application Status:"
	@curl -s http://localhost:3000/api/v1/health || echo "Health check failed"
	@echo "\nDatabase Status:"
	@curl -s http://localhost:3000/api/v1/health/database || echo "Database check failed"
	@echo "\nCache Status:"
	@curl -s http://localhost:3000/api/v1/health/cache || echo "Cache check failed"
	@echo "\nDocker Services:"
	@docker ps --filter "name=mongo-dev" --filter "name=redis-dev" || echo "No development containers running"
