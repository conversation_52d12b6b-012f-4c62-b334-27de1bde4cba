# Makefile for Cashback Comparison Platform

.PHONY: help install build start start-dev stop clean test lint format docker-build docker-up docker-down logs

# Default target
help:
	@echo "🚀 Cashback Comparison Platform - Development Commands"
	@echo "======================================================"
	@echo ""
	@echo "📦 DESENVOLVIMENTO (RECOMENDADO - TUDO COM DOCKER):"
	@echo "  dev              - Inicia TODA a aplicação com Docker"
	@echo "  stop             - Para todos os serviços"
	@echo "  logs             - Ver logs da aplicação"
	@echo "  restart          - Reinicia a aplicação"
	@echo ""
	@echo "🧪 TESTES:"
	@echo "  test             - Testes unitários"
	@echo "  test-api         - Testes de integração da API"
	@echo ""
	@echo "🔧 UTILITÁRIOS:"
	@echo "  build            - Build da aplicação"
	@echo "  clean            - Limpar containers e volumes"
	@echo "  health           - Verificar saúde da aplicação"
	@echo "  monitor          - Status completo do sistema"
	@echo ""
	@echo "🌐 ACESSO:"
	@echo "  API: http://localhost:3000/api/v1"
	@echo "  Docs: http://localhost:3000/docs"
	@echo "  MongoDB UI: http://localhost:8081"
	@echo "  Redis UI: http://localhost:8082"

# 📦 COMANDOS PRINCIPAIS DE DESENVOLVIMENTO
dev:
	@echo "🚀 Iniciando aplicação completa com Docker..."
	docker-compose -f docker-compose.dev.yml up -d
	@echo "✅ Aplicação iniciada!"
	@echo "🌐 Acesse: http://localhost:3000/api/v1"
	@echo "📚 Docs: http://localhost:3000/docs"

stop:
	@echo "🛑 Parando todos os serviços..."
	docker-compose -f docker-compose.dev.yml down
	@echo "✅ Serviços parados!"

restart:
	@echo "🔄 Reiniciando aplicação..."
	docker-compose -f docker-compose.dev.yml restart app
	@echo "✅ Aplicação reiniciada!"

logs:
	@echo "📋 Logs da aplicação:"
	docker-compose -f docker-compose.dev.yml logs -f app

# 🔧 COMANDOS DE BUILD E LIMPEZA
build:
	npm run build

clean:
	@echo "🧹 Limpando containers e volumes..."
	docker-compose -f docker-compose.dev.yml down -v
	docker system prune -f
	@echo "✅ Limpeza concluída!"

test:
	npm run test

test-e2e:
	npm run test:e2e

test-cov:
	npm run test:cov

test-api:
	@echo "Running API integration tests..."
	./test-api.sh

lint:
	npm run lint

format:
	npm run format

# 🔍 COMANDOS DE MONITORAMENTO
health:
	@echo "🏥 Verificando saúde da aplicação..."
	@curl -f http://localhost:3000/api/v1/health || echo "❌ Aplicação não está saudável"

monitor:
	@echo "📊 Status do Sistema:"
	@echo "===================="
	@echo "🏥 Saúde da Aplicação:"
	@curl -s http://localhost:3000/api/v1/health || echo "❌ Aplicação offline"
	@echo ""
	@echo "💾 Status do Banco:"
	@curl -s http://localhost:3000/api/v1/health/database || echo "❌ Banco offline"
	@echo ""
	@echo "⚡ Status do Cache:"
	@curl -s http://localhost:3000/api/v1/health/cache || echo "❌ Cache offline"
	@echo ""
	@echo "🐳 Containers Docker:"
	@docker-compose -f docker-compose.dev.yml ps

# Database commands
db-seed:
	npm run db:seed

db-migrate:
	npm run db:migrate

db-reset:
	npm run db:reset

# Utility commands
logs:
	tail -f logs/app.log

logs-error:
	tail -f logs/error.log

health:
	curl -f http://localhost:3000/api/v1/health || echo "Service is not healthy"

# Production deployment
deploy-prod:
	@echo "Deploying to production..."
	docker-compose -f docker-compose.yml up -d --build

# 🚀 COMANDOS DE SETUP (APENAS SE NECESSÁRIO)
setup:
	@echo "⚙️ Configurando ambiente de desenvolvimento..."
	@cp .env.development .env 2>/dev/null || echo "Arquivo .env já existe"
	@echo "✅ Setup concluído!"
	@echo "💡 Execute 'make dev' para iniciar a aplicação"

# CI/CD helpers
ci-test:
	npm ci
	npm run test
	npm run test:e2e

ci-build:
	npm run build
	docker build -t cashback-comparison-platform .

# Monitoring
monitor:
	@echo "Application Status:"
	@curl -s http://localhost:3000/api/v1/health || echo "Health check failed"
	@echo "\nDatabase Status:"
	@curl -s http://localhost:3000/api/v1/health/database || echo "Database check failed"
	@echo "\nCache Status:"
	@curl -s http://localhost:3000/api/v1/health/cache || echo "Cache check failed"
	@echo "\nDocker Services:"
	@docker ps --filter "name=mongo-dev" --filter "name=redis-dev" || echo "No development containers running"
