# 🔧 DEV ENVIRONMENT
# Para ambiente de desenvolvimento compartilhado da equipe

# Application Configuration
NODE_ENV=development
PORT=4000
API_PREFIX=api/v1

# Database Configuration (Docker/Cloud)
MONGODB_URI=mongodb://mongo:27017/cashback-comparison-dev
MONGODB_TEST_URI=mongodb://mongo:27017/cashback-comparison-dev-test

# Redis Configuration (Docker/Cloud)
REDIS_HOST=redis
REDIS_PORT=6379
REDIS_PASSWORD=
REDIS_DB=1
REDIS_KEY_PREFIX=cashback:dev:

# Cache Configuration
CACHE_COMPARISON_TTL=300
CACHE_OFFERS_TTL=1800
CACHE_PLATFORMS_TTL=3600
CACHE_ANALYTICS_TTL=900
CACHE_HEALTH_TTL=60
CACHE_MAX_COMPARISONS=1000
CACHE_MAX_OFFERS=5000
CACHE_MAX_PLATFORMS=50
CACHE_MAX_ANALYTICS=100

# Rate Limiting (Permissivo para testes)
THROTTLE_TTL=60
THROTTLE_LIMIT=1000

# Logging
LOG_LEVEL=debug
LOG_FILE_PATH=logs/app-dev.log

# External APIs (Sandbox)
# SAMPLE_PLATFORM_API_KEY=dev_sandbox_key

# Development Features
ENABLE_SWAGGER=true
ENABLE_DEBUG_ROUTES=true
CORS_ORIGIN=*

# New Relic Configuration
NEW_RELIC_ENABLED=true
NEW_RELIC_APP_NAME=Cashback-Platform-Dev
NEW_RELIC_LICENSE_KEY=${NEW_RELIC_DEV_LICENSE_KEY}
NEW_RELIC_LOG_LEVEL=info
