# 🌍 Gestão de Ambientes - Cashback Platform

## 🎯 **Estratégia de Ambientes**

### **LOCAL** 💻 - Desenvolvimento Individual
- **Propósito**: Desenvolvimento rápido e debug
- **Infraestrutura**: App local + MongoDB/Redis Docker
- **Dados**: Dados de teste locais
- **Acesso**: Apenas desenvolvedor

### **DEV** 🔧 - Integração da Equipe  
- **Propósito**: Integração contínua e testes da equipe
- **Infraestrutura**: Docker completo
- **Dados**: Dados compartilhados de desenvolvimento
- **Acesso**: Toda equipe de desenvolvimento

### **UAT** 🧪 - Homologação
- **Propósito**: Testes de aceitação e validação
- **Infraestrutura**: Docker/Cloud (similar à produção)
- **Dados**: Dados similares à produção (anonimizados)
- **Acesso**: QA, Product Owners, Stakeholders

### **PROD** 🚀 - Produção
- **Propósito**: Ambiente de produção
- **Infraestrutura**: Cloud/Kubernetes
- **Dados**: Dados reais dos usuários
- **Acesso**: Usuários finais

## 🔧 **Configuração por Ambiente**

### **LOCAL** 💻
```bash
# Configurar ambiente
make env-local

# Executar
make local-dev

# Acesso
http://localhost:3001/api/v1
```

**Características:**
- ✅ Hot reload instantâneo
- ✅ Debug completo
- ✅ Logs detalhados
- ✅ Rate limiting permissivo
- ✅ Cache com TTL baixo

### **DEV** 🔧
```bash
# Configurar ambiente
make env-dev

# Executar
make deploy-dev

# Acesso
http://localhost:3000/api/v1
```

**Características:**
- ✅ Docker completo
- ✅ Swagger habilitado
- ✅ Debug routes ativas
- ✅ CORS permissivo
- ✅ Logs debug

### **UAT** 🧪
```bash
# Configurar ambiente
make env-uat

# Executar
make deploy-uat

# Acesso
http://uat.cashboost.com.br/api/v1
```

**Características:**
- ✅ Nginx como proxy
- ✅ SSL/HTTPS
- ✅ Monitoring (Prometheus)
- ✅ Rate limiting restritivo
- ✅ Logs info level

### **PROD** 🚀
```bash
# Configurar ambiente
make env-prod

# Executar (com confirmação)
make deploy-prod

# Acesso
https://cashboost.com.br/api/v1
```

**Características:**
- ✅ Kubernetes/Cloud
- ✅ Load balancing
- ✅ Auto-scaling
- ✅ Monitoring completo
- ✅ Logs apenas warnings/errors

## 📊 **Comparação de Configurações**

| Configuração | LOCAL | DEV | UAT | PROD |
|--------------|-------|-----|-----|------|
| **Porta** | 3001 | 3000 | 3000 | 3000 |
| **Logs** | debug | debug | info | warn |
| **Swagger** | ❌ | ✅ | ✅ | ❌ |
| **Debug Routes** | ❌ | ✅ | ❌ | ❌ |
| **Rate Limit** | 10000/min | 1000/min | 100/min | 100/min |
| **Cache TTL** | Baixo | Médio | Alto | Alto |
| **CORS** | * | * | Restrito | Restrito |
| **SSL** | ❌ | ❌ | ✅ | ✅ |
| **Monitoring** | ❌ | ❌ | ✅ | ✅ |

## 🚀 **Workflow de Deploy**

### **1. Desenvolvimento Local**
```bash
# Configurar uma vez
make env-local
make local-install

# Desenvolvimento diário
make local-dev
```

### **2. Push para DEV**
```bash
# Após commit/push
make env-dev
make deploy-dev

# Testar integração
curl http://localhost:3000/api/v1/health
```

### **3. Promoção para UAT**
```bash
# Após aprovação em DEV
make env-uat
make deploy-uat

# Testes de aceitação
curl http://uat.cashboost.com.br/api/v1/health
```

### **4. Deploy para PROD**
```bash
# Após aprovação em UAT
make env-prod
make deploy-prod  # Pede confirmação

# Verificar produção
curl https://cashboost.com.br/api/v1/health
```

## 🔐 **Gestão de Secrets**

### **Desenvolvimento (LOCAL/DEV)**
- Secrets no arquivo `.env`
- Valores de desenvolvimento/sandbox

### **Staging (UAT)**
- Secrets via variáveis de ambiente
- Valores de staging

### **Produção (PROD)**
- Secrets via Kubernetes Secrets
- Valores de produção seguros

```bash
# Exemplo para UAT/PROD
export MONGODB_PROD_URI="mongodb+srv://..."
export REDIS_PROD_PASSWORD="..."
export JWT_PROD_SECRET="..."
```

## 📁 **Estrutura de Arquivos**

```
├── .env.local          # Configuração LOCAL
├── .env.dev            # Configuração DEV
├── .env.uat            # Configuração UAT
├── .env.prod           # Configuração PROD
├── docker-compose.yml  # DEV (padrão)
├── docker-compose.local.yml    # LOCAL (deps apenas)
├── docker-compose.uat.yml      # UAT
├── docker-compose.prod.yml     # PROD
├── Dockerfile          # Produção
├── Dockerfile.dev      # Desenvolvimento
└── Makefile           # Comandos para todos ambientes
```

## 🎯 **Comandos Rápidos**

```bash
# 📋 AJUDA
make help

# 💻 LOCAL
make env-local && make local-dev

# 🔧 DEV
make env-dev && make deploy-dev

# 🧪 UAT
make env-uat && make deploy-uat

# 🚀 PROD
make env-prod && make deploy-prod

# 🔍 STATUS
make monitor
```

## 🌟 **Benefícios da Estratégia**

### **1. Isolamento Completo**
- Cada ambiente é independente
- Configurações específicas
- Dados isolados

### **2. Progressão Controlada**
- LOCAL → DEV → UAT → PROD
- Validação em cada etapa
- Rollback fácil

### **3. Desenvolvimento Eficiente**
- LOCAL: Máxima velocidade
- DEV: Integração contínua
- UAT: Validação realista
- PROD: Máxima confiabilidade

### **4. Operação Simplificada**
- Comandos padronizados
- Deploy automatizado
- Monitoramento integrado

---

**💡 Dica**: Sempre teste em LOCAL → DEV → UAT antes de PROD!
