import { Injectable, Logger } from '@nestjs/common';
import { ConfigService } from '@nestjs/config';

@Injectable()
export class NewRelicService {
  private readonly logger = new Logger(NewRelicService.name);
  private newrelic: any;

  constructor(private configService: ConfigService) {
    this.initializeNewRelic();
  }

  private initializeNewRelic() {
    const licenseKey = this.configService.get<string>('NEW_RELIC_LICENSE_KEY');
    const appName = this.configService.get<string>('NEW_RELIC_APP_NAME');
    const enabled = this.configService.get<boolean>('NEW_RELIC_ENABLED', false);

    if (!enabled || !licenseKey) {
      this.logger.warn('New Relic is disabled or license key not provided');
      return;
    }

    try {
      this.newrelic = require('newrelic');
      this.logger.log(`New Relic initialized for app: ${appName}`);
    } catch (error) {
      this.logger.error('Failed to initialize New Relic', error);
    }
  }

  /**
   * Record a custom metric
   */
  recordMetric(name: string, value: number): void {
    if (this.newrelic) {
      this.newrelic.recordMetric(name, value);
    }
  }

  /**
   * Record a custom event
   */
  recordCustomEvent(eventType: string, attributes: Record<string, any>): void {
    if (this.newrelic) {
      this.newrelic.recordCustomEvent(eventType, attributes);
    }
  }

  /**
   * Add custom attributes to the current transaction
   */
  addCustomAttributes(attributes: Record<string, any>): void {
    if (this.newrelic) {
      this.newrelic.addCustomAttributes(attributes);
    }
  }

  /**
   * Set the name of the current transaction
   */
  setTransactionName(category: string, name: string): void {
    if (this.newrelic) {
      this.newrelic.setTransactionName(category, name);
    }
  }

  /**
   * Notice an error
   */
  noticeError(error: Error, customAttributes?: Record<string, any>): void {
    if (this.newrelic) {
      this.newrelic.noticeError(error, customAttributes);
    }
  }

  /**
   * Start a web transaction
   */
  startWebTransaction(url: string, handle: Function): any {
    if (this.newrelic) {
      return this.newrelic.startWebTransaction(url, handle);
    }
    return handle();
  }

  /**
   * Start a background transaction
   */
  startBackgroundTransaction(name: string, group: string, handle: Function): any {
    if (this.newrelic) {
      return this.newrelic.startBackgroundTransaction(name, group, handle);
    }
    return handle();
  }

  /**
   * Create a distributed trace payload
   */
  createDistributedTracePayload(): any {
    if (this.newrelic) {
      return this.newrelic.createDistributedTracePayload();
    }
    return null;
  }

  /**
   * Accept a distributed trace payload
   */
  acceptDistributedTracePayload(payload: any): void {
    if (this.newrelic) {
      this.newrelic.acceptDistributedTracePayload(payload);
    }
  }

  /**
   * Get the current transaction
   */
  getTransaction(): any {
    if (this.newrelic) {
      return this.newrelic.getTransaction();
    }
    return null;
  }

  /**
   * Increment a counter metric
   */
  incrementMetric(name: string, value: number = 1): void {
    if (this.newrelic) {
      this.newrelic.incrementMetric(name, value);
    }
  }

  /**
   * Record database query metrics
   */
  recordDatabaseQuery(operation: string, collection: string, duration: number): void {
    this.recordMetric(`Database/${operation}/${collection}/Duration`, duration);
    this.incrementMetric(`Database/${operation}/${collection}/Count`);
  }

  /**
   * Record cache operation metrics
   */
  recordCacheOperation(operation: string, hit: boolean, duration: number): void {
    const status = hit ? 'Hit' : 'Miss';
    this.recordMetric(`Cache/${operation}/${status}/Duration`, duration);
    this.incrementMetric(`Cache/${operation}/${status}/Count`);
  }

  /**
   * Record API endpoint metrics
   */
  recordApiEndpoint(method: string, endpoint: string, statusCode: number, duration: number): void {
    this.recordMetric(`API/${method}${endpoint}/Duration`, duration);
    this.incrementMetric(`API/${method}${endpoint}/Count`);
    this.incrementMetric(`API/StatusCode/${statusCode}/Count`);
  }
}
