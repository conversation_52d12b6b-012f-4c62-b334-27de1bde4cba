import {
  Injectable,
  NestInterceptor,
  Execution<PERSON>ontext,
  CallHandler,
} from '@nestjs/common';
import { Observable } from 'rxjs';
import { tap, catchError } from 'rxjs/operators';
import { NewRelicService } from './newrelic.service';

@Injectable()
export class NewRelicInterceptor implements NestInterceptor {
  constructor(private readonly newRelicService: NewRelicService) {}

  intercept(context: ExecutionContext, next: CallHandler): Observable<any> {
    const request = context.switchToHttp().getRequest();
    const response = context.switchToHttp().getResponse();
    
    const startTime = Date.now();
    const method = request.method;
    const url = request.url;
    const userAgent = request.get('User-Agent') || '';
    const ip = request.ip || request.connection.remoteAddress;

    // Add custom attributes to the transaction
    this.newRelicService.addCustomAttributes({
      'request.method': method,
      'request.url': url,
      'request.userAgent': userAgent,
      'request.ip': ip,
      'request.userId': request.user?.id || 'anonymous',
    });

    // Set transaction name
    const controllerName = context.getClass().name;
    const handlerName = context.getHandler().name;
    this.newRelicService.setTransactionName('Controller', `${controllerName}.${handlerName}`);

    return next.handle().pipe(
      tap(() => {
        const duration = Date.now() - startTime;
        const statusCode = response.statusCode;

        // Record API endpoint metrics
        this.newRelicService.recordApiEndpoint(method, url, statusCode, duration);

        // Record custom event for successful requests
        this.newRelicService.recordCustomEvent('ApiRequest', {
          method,
          url,
          statusCode,
          duration,
          controller: controllerName,
          handler: handlerName,
          userAgent,
          ip,
          userId: request.user?.id || 'anonymous',
        });
      }),
      catchError((error) => {
        const duration = Date.now() - startTime;
        const statusCode = error.status || 500;

        // Record error metrics
        this.newRelicService.recordApiEndpoint(method, url, statusCode, duration);
        
        // Notice the error to New Relic
        this.newRelicService.noticeError(error, {
          method,
          url,
          statusCode,
          duration,
          controller: controllerName,
          handler: handlerName,
          userAgent,
          ip,
          userId: request.user?.id || 'anonymous',
        });

        // Record custom event for failed requests
        this.newRelicService.recordCustomEvent('ApiError', {
          method,
          url,
          statusCode,
          duration,
          controller: controllerName,
          handler: handlerName,
          errorMessage: error.message,
          errorStack: error.stack,
          userAgent,
          ip,
          userId: request.user?.id || 'anonymous',
        });

        throw error;
      }),
    );
  }
}
