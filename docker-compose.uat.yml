# 🧪 UAT ENVIRONMENT
# Para testes de aceitação e homologação

services:
  # Aplicação NestJS
  app:
    build:
      context: .
      dockerfile: Dockerfile
      target: production
    container_name: cashback-app-uat
    restart: unless-stopped
    ports:
      - "3000:3000"
    environment:
      - NODE_ENV=staging
      - MONGODB_URI=mongodb://mongo:27017/cashback-comparison-uat
      - REDIS_HOST=redis
      - REDIS_PORT=6379
    env_file:
      - .env.uat
    volumes:
      - ./logs:/app/logs
    depends_on:
      - mongo
      - redis
    networks:
      - cashback-uat
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:3000/api/v1/health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

  # MongoDB
  mongo:
    image: mongo:7.0
    container_name: cashback-mongo-uat
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=cashback-comparison-uat
    volumes:
      - mongo_uat_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - cashback-uat

  # Redis
  redis:
    image: redis:7.2-alpine
    container_name: cashback-redis-uat
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_uat_data:/data
    networks:
      - cashback-uat
    command: redis-server --appendonly yes

  # Nginx (Load Balancer/Reverse Proxy)
  nginx:
    image: nginx:alpine
    container_name: cashback-nginx-uat
    restart: unless-stopped
    ports:
      - "80:80"
      - "443:443"
    volumes:
      - ./nginx/uat.conf:/etc/nginx/nginx.conf:ro
      - ./ssl:/etc/ssl/certs:ro
    depends_on:
      - app
    networks:
      - cashback-uat

  # Monitoring - Prometheus (opcional)
  prometheus:
    image: prom/prometheus:latest
    container_name: cashback-prometheus-uat
    restart: unless-stopped
    ports:
      - "9090:9090"
    volumes:
      - ./monitoring/prometheus.yml:/etc/prometheus/prometheus.yml:ro
      - prometheus_uat_data:/prometheus
    networks:
      - cashback-uat
    profiles:
      - monitoring

volumes:
  mongo_uat_data:
    driver: local
  redis_uat_data:
    driver: local
  prometheus_uat_data:
    driver: local

networks:
  cashback-uat:
    driver: bridge
