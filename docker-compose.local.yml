# 💻 LOCAL DEVELOPMENT
# Para desenvolvimento individual (apenas dependências)

services:
  # MongoDB para desenvolvimento local
  mongo:
    image: mongo:7.0
    container_name: cashback-mongo-local
    restart: unless-stopped
    ports:
      - "27017:27017"
    environment:
      - MONGO_INITDB_DATABASE=cashback-comparison-local
    volumes:
      - mongo_local_data:/data/db
      - ./scripts/mongo-init.js:/docker-entrypoint-initdb.d/mongo-init.js:ro
    networks:
      - cashback-local

  # Redis para desenvolvimento local
  redis:
    image: redis:7.2-alpine
    container_name: cashback-redis-local
    restart: unless-stopped
    ports:
      - "6379:6379"
    volumes:
      - redis_local_data:/data
    networks:
      - cashback-local
    command: redis-server --appendonly yes

  # MongoDB Express (opcional para debug)
  mongo-express:
    image: mongo-express:1.0.2
    container_name: cashback-mongo-express-local
    restart: unless-stopped
    ports:
      - "8081:8081"
    environment:
      - ME_CONFIG_MONGODB_SERVER=mongo
      - ME_CONFIG_MONGODB_PORT=27017
      - ME_CONFIG_MONGODB_ADMINUSERNAME=
      - ME_CONFIG_MONGODB_ADMINPASSWORD=
      - ME_CONFIG_BASICAUTH_USERNAME=admin
      - ME_CONFIG_BASICAUTH_PASSWORD=admin123
    depends_on:
      - mongo
    networks:
      - cashback-local
    profiles:
      - tools

  # Redis Commander (opcional para debug)
  redis-commander:
    image: rediscommander/redis-commander:latest
    container_name: cashback-redis-commander-local
    restart: unless-stopped
    ports:
      - "8082:8081"
    environment:
      - REDIS_HOSTS=local:redis:6379
    depends_on:
      - redis
    networks:
      - cashback-local
    profiles:
      - tools

volumes:
  mongo_local_data:
    driver: local
  redis_local_data:
    driver: local

networks:
  cashback-local:
    driver: bridge
